<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;
use Illuminate\Support\Facades\DB;

class AddSortingToCommitmentSteps extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::table('commitment_steps', function (Blueprint $table) {
            $table->integer('sort_order');
        });

		// Set default sort order (just copy ID to sort order)
		DB::statement('UPDATE commitments SET sort_order = id');
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::table('commitment_steps', function (Blueprint $table) {
            $table->dropColumn('sort_order');
        });
    }
}
