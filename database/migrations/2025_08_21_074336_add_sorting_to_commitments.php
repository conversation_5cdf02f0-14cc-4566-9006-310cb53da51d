<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Schema;

class AddSortingToCommitments extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        // Add order column to the model
		Schema::table('commitments', function (Blueprint $table) {
			$table->integer('sort_order');
		});

		// Set default sort order (just copy ID to sort order)
		DB::statement('UPDATE commitments SET sort_order = id');
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::table('commitments', function (Blueprint $table) {
            $table->dropColumn('sort_order');
        });
    }
}
