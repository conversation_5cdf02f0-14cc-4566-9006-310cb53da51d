<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsToMany;
use Spatie\SchemalessAttributes\SchemalessAttributesTrait;
use Spatie\EloquentSortable\Sortable;
use Spatie\EloquentSortable\SortableTrait;

class Commitment extends Model implements Sortable
{
    use HasFactory, SchemalessAttributesTrait, SortableTrait;

	protected $schemalessAttributes = [
		'sdg_outcomes',
		'gbf_outcomes'
	];

	protected $appends = [
		'city_count',
	];

	//Settings
	public $sortable = [
		'order_column_name' => 'sort_order',
		'sort_when_creating' => true,
  		'sort_on_has_many' => true,
	];

	//Relationships
	public function theme() {
		return $this->belongsTo(CommitmentTheme::class, 'commitment_theme_id');
	}

	public function commitment_steps() {
		return $this->hasMany(CommitmentStep::class);
	}

	public function cities() : BelongsToMany
	{
		return $this->belongsToMany(City::class);
	}

	public function sdg_targets() : BelongsToMany
	{
		return $this->belongsToMany(SustainableDevelopmentGoal::class, 'commitment_sustainable_development_goal', 'commitment_id', 'sdg_id');
	}

	public function gbf_targets() : BelongsToMany
	{
		return $this->belongsToMany(GlobalBiodiversityTarget::class, 'commitment_global_biodiversity_target', 'commitment_id', 'gbf_id');
	}

	//Accessorts
	public function getCityCountAttribute() {
		return $this->cities->where('active', true)->count();
	}
}
