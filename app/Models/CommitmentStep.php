<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsToMany;
use Spatie\EloquentSortable\Sortable;
use Spatie\EloquentSortable\SortableTrait;

class CommitmentStep extends Model implements Sortable
{
    use HasFactory, SortableTrait;

	protected $guarded = [];

	protected $appends = [
		'participating_cities'
	];

	//Settings
	public $sortable = [
		'order_column_name' => 'sort_order',
		'sort_when_creating' => true,
  		'sort_on_has_many' => true,
	];

	//Relationships
	public function commitment()
	{
		return $this->belongsTo(Commitment::class);
	}

	public function commitment_targets() : BelongsToMany
	{
		return $this->belongsToMany(CommitmentStepTarget::class, 'step_commitment_step_target');
	}

	public function cities() : BelongsToMany
	{
		return $this->belongsToMany(City::class);
	}

	//Accessors
	public function getParticipatingCitiesAttribute() {
		return $this
			->commitment_targets
			->flatMap(function ($target) {
				return $target->commitment_actions->pluck('city');
			})
			->unique()
			->filter(function ($city) {
				return $city->active;
			});
	}
}
