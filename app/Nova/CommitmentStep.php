<?php

namespace App\Nova;

use Illuminate\Http\Request;
use <PERSON>vel\Nova\Fields\BelongsTo;
use <PERSON><PERSON>\Nova\Fields\BelongsToMany;
use <PERSON><PERSON>\Nova\Fields\ID;
use <PERSON><PERSON>\Nova\Fields\Text;
use <PERSON>vel\Nova\Fields\Textarea;
use <PERSON><PERSON>\Nova\Http\Requests\NovaRequest;
use OptimistDigital\NovaSortable\Traits\HasSortableRows;

class CommitmentStep extends Resource
{
	use HasSortableRows;
	
	public static $group = 'Action Platform';

    /**
     * The model the resource corresponds to.
     *
     * @var string
     */
    public static $model = \App\Models\CommitmentStep::class;

    /**
     * The single value that should be used to represent the resource when being displayed.
     *
     * @var string
     */
    public static $title = 'title';

    /**
     * The columns that should be searched.
     *
     * @var array
     */
    public static $search = [
        'title',
    ];

    /**
     * Get the fields displayed by the resource.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return array
     */
    public function fields(Request $request)
    {
        return [
            ID::make(__('ID'), 'id')->sortable(),
			Text::make('title'),
			Textarea::make('description'),
			BelongsTo::make('Commitment'),
			BelongsToMany::make('Commitment Targets', 'commitment_targets', CommitmentStepTarget::class)
        ];
    }

    /**
     * Get the cards available for the request.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return array
     */
    public function cards(Request $request)
    {
        return [];
    }

    /**
     * Get the filters available for the resource.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return array
     */
    public function filters(Request $request)
    {
        return [];
    }

    /**
     * Get the lenses available for the resource.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return array
     */
    public function lenses(Request $request)
    {
        return [];
    }

    /**
     * Get the actions available for the resource.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return array
     */
    public function actions(Request $request)
    {
        return [];
    }
}
